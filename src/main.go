package main

import (
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"strconv"
	"time"

	"github.com/gorilla/mux"
)

type Tasks struct {
	ID         string `json:"id"`
	TaskName   string `json:"task_name"`
	TaskDetail string `json:"task_detail"`
	Date       string `json:"date"`
}

var tasks []Tasks

func allTasks() {
	task := Tasks{
		ID:         "1",
		TaskName:   "Task 1",
		TaskDetail: "Task 1 Detail",
		Date:       "2020-01-01",
	}

	tasks = append(tasks, task)
	fmt.Println("your tasks are: ", tasks)
}

func homePage(w http.ResponseWriter, r *http.Request) {
	fmt.Fprintf(w, "Welcome to the HomePage ???")
	fmt.Println("Endpoint Hit: homePage")
}

func getTasks(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(tasks)
	fmt.Println("Endpoint Hit: getTasks")
}

func getTask(w http.ResponseWriter, r *http.Request) {
	taskId := mux.Vars(r)
	flag := false
	for i := 0; i < len(tasks); i++ {
		if tasks[i].ID == taskId["id"] {
			flag = true
			json.NewEncoder(w).Encode(tasks[i])
			break
		}
	}
	if !flag {
		json.NewEncoder(w).Encode("No task found")
	}
	fmt.Println("Endpoint Hit: getTask")
}

func createTask(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	var task Tasks
	_ = json.NewDecoder(r.Body).Decode(&task)
	task.ID = strconv.Itoa(rand.Intn(1000))
	currentTime := time.Now().Format("2006-01-02")
	task.Date = currentTime
	tasks = append(tasks, task)
	json.NewEncoder(w).Encode(tasks)

	fmt.Println("Endpoint Hit: createTasks")
}

func deleteTask(w http.ResponseWriter, r *http.Request) {
	fmt.Println("Endpoint Hit: getTasks")
}

func updateTask(w http.ResponseWriter, r *http.Request) {
	fmt.Println("Endpoint Hit: getTasks")
}

func handleRoutes() {
	router := mux.NewRouter()
	router.HandleFunc("/", homePage).Methods("GET")
	router.HandleFunc("/gettasks", getTasks).Methods("GET")
	router.HandleFunc("/gettask/{id}", getTask).Methods("GET")
	router.HandleFunc("/create", createTask).Methods("POST")
	router.HandleFunc("/delete/{id}", deleteTask).Methods("DELETE")
	router.HandleFunc("/update/{id}", updateTask).Methods("PUT")
	log.Fatal(http.ListenAndServe(":8082", router))
}

func main() {
	allTasks()
	handleRoutes()
	fmt.Println("Hello, World!")
}
